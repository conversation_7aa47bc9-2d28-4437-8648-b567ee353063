"""
Sistema de movimento automático do campeão
Integrado com combate e sistema de lanes
"""
import time
import random
import math
from typing import Tuple, List, Optional
from utils.logger import logger
from utils.input_utils import input_controller
from game.lol_detector import lol_detector
from game.combat_system import combat_system
from game.lane_system import lane_system, Lane

class AutoMovement:
    """Sistema de movimento automático do campeão"""
    
    def __init__(self):
        self.is_moving = False
        self.current_target = None
        self.movement_pattern = "random"  # random, circle, lane, farm
        self.last_move_time = 0
        self.move_interval = 2.0  # Segundos entre movimentos
        
        # Posições base (centro da tela como referência)
        self.screen_center = (960, 540)
        self.safe_zone_radius = 200
        self.movement_radius = 150
        
        # Padrões de movimento
        self.circle_angle = 0
        self.lane_positions = [
            (400, 600),   # Bot lane
            (500, 500),   # Mid lane
            (600, 400),   # Top lane
        ]
        self.current_lane_index = 0
        
        logger.info("Sistema de movimento automático inicializado")
    
    def start_auto_movement(self, pattern: str = "random"):
        """Iniciar movimento automático"""
        self.movement_pattern = pattern
        self.is_moving = True
        logger.info(f"Movimento automático iniciado - Padrão: {pattern}")
    
    def stop_auto_movement(self):
        """Parar movimento automático"""
        self.is_moving = False
        logger.info("Movimento automático parado")
    
    def update_movement(self):
        """Atualizar movimento inteligente (chamar em loop)"""
        if not self.is_moving:
            return

        current_time = time.time()

        # Verificar se LoL está pronto
        if not lol_detector.is_lol_running():
            logger.warning("LoL não está rodando - pausando movimento")
            return

        # PRIORIDADE 1: Sistema de combate
        combat_action = combat_system.update_combat()
        if combat_action:
            logger.debug("🎯 Ação de combate executada")
            self.last_move_time = current_time
            return

        # PRIORIDADE 2: Movimento baseado no padrão (se não há combate)
        if current_time - self.last_move_time < self.move_interval:
            return

        try:
            if self.movement_pattern == "lane":
                self._move_lane_intelligent()
            elif self.movement_pattern == "farm":
                self._move_farm_intelligent()
            elif self.movement_pattern == "random":
                self._move_random()
            elif self.movement_pattern == "circle":
                self._move_circle()

            self.last_move_time = current_time

        except Exception as e:
            logger.error(f"Erro no movimento automático: {e}")
    
    def _move_random(self):
        """Movimento aleatório"""
        # Gerar posição aleatória próxima ao centro
        center_x, center_y = self.screen_center
        
        # Ângulo aleatório
        angle = random.uniform(0, 2 * math.pi)
        
        # Distância aleatória dentro do raio
        distance = random.uniform(50, self.movement_radius)
        
        # Calcular nova posição
        new_x = center_x + int(distance * math.cos(angle))
        new_y = center_y + int(distance * math.sin(angle))
        
        # Garantir que está dentro da tela
        new_x = max(100, min(1820, new_x))
        new_y = max(100, min(980, new_y))
        
        self._move_to_position(new_x, new_y)
        logger.debug(f"Movimento aleatório para ({new_x}, {new_y})")
    
    def _move_circle(self):
        """Movimento em círculo"""
        center_x, center_y = self.screen_center
        
        # Incrementar ângulo
        self.circle_angle += 0.3  # Velocidade do círculo
        if self.circle_angle >= 2 * math.pi:
            self.circle_angle = 0
        
        # Calcular posição no círculo
        radius = self.movement_radius
        new_x = center_x + int(radius * math.cos(self.circle_angle))
        new_y = center_y + int(radius * math.sin(self.circle_angle))
        
        self._move_to_position(new_x, new_y)
        logger.debug(f"Movimento circular para ({new_x}, {new_y}) - Ângulo: {self.circle_angle:.2f}")
    
    def _move_lane_intelligent(self):
        """Movimento inteligente baseado em lanes"""
        # Obter próxima posição da lane
        target_pos = lane_system.update_lane_movement()

        self._move_to_position(target_pos[0], target_pos[1])

        lane_status = lane_system.get_current_status()
        logger.debug(f"Movimento lane inteligente: {lane_status['current_lane']} - {lane_status['current_position']}")

    def _move_farm_intelligent(self):
        """Movimento inteligente para farm"""
        # Obter posição de farm da lane atual
        farm_pos = lane_system.get_farm_position()

        self._move_to_position(farm_pos[0], farm_pos[1])
        logger.debug(f"Movimento farm inteligente para ({farm_pos[0]}, {farm_pos[1]})")

    def _move_lane(self):
        """Movimento entre lanes (método antigo)"""
        # Alternar entre posições de lane
        target_pos = self.lane_positions[self.current_lane_index]

        # Adicionar variação aleatória
        variation_x = random.randint(-50, 50)
        variation_y = random.randint(-50, 50)

        new_x = target_pos[0] + variation_x
        new_y = target_pos[1] + variation_y

        # Garantir limites da tela
        new_x = max(100, min(1820, new_x))
        new_y = max(100, min(980, new_y))

        self._move_to_position(new_x, new_y)

        # Próxima lane
        self.current_lane_index = (self.current_lane_index + 1) % len(self.lane_positions)

        logger.debug(f"Movimento lane para ({new_x}, {new_y}) - Lane: {self.current_lane_index}")

    def _move_farm(self):
        """Movimento para farm (método antigo)"""
        center_x, center_y = self.screen_center

        # Movimento mais próximo ao centro (mais seguro)
        angle = random.uniform(0, 2 * math.pi)
        distance = random.uniform(30, 100)  # Distância menor

        new_x = center_x + int(distance * math.cos(angle))
        new_y = center_y + int(distance * math.sin(angle))

        # Garantir limites
        new_x = max(200, min(1720, new_x))
        new_y = max(200, min(880, new_y))

        self._move_to_position(new_x, new_y)
        logger.debug(f"Movimento farm para ({new_x}, {new_y})")
    
    def _move_to_position(self, x: int, y: int):
        """Mover para posição específica sem perder foco"""
        try:
            # Verificar se LoL ainda está em foco (sem forçar foco)
            if not self._is_lol_focused():
                logger.warning("LoL perdeu foco - pausando movimento")
                return

            # Movimento humanizado mais suave
            input_controller.move_mouse_humanized(x, y)
            time.sleep(0.05)  # Delay menor

            # Clique direito para mover (sem mudança de foco)
            input_controller.right_click_at(x, y)

            self.current_target = (x, y)

        except Exception as e:
            logger.error(f"Erro ao mover para ({x}, {y}): {e}")

    def _is_lol_focused(self) -> bool:
        """Verificar se LoL está em foco sem tentar focar"""
        try:
            import win32gui
            current_window = win32gui.GetForegroundWindow()
            lol_window = lol_detector.lol_window

            if lol_window and current_window == lol_window:
                return True
            else:
                return False

        except Exception:
            return True  # Assumir que está em foco se não conseguir verificar
    
    def move_to_safe_position(self):
        """Mover para posição segura (próxima à torre)"""
        # Usar sistema de lanes para posição segura
        safe_pos = lane_system.get_safe_position()

        self._move_to_position(safe_pos[0], safe_pos[1])
        logger.info(f"🛡️ Movendo para posição segura: {safe_pos}")

    def retreat_to_tower(self):
        """Recuar para torre (ainda mais seguro)"""
        retreat_pos = lane_system.get_retreat_position()

        self._move_to_position(retreat_pos[0], retreat_pos[1])
        logger.info(f"🏃 RECUANDO PARA TORRE: {retreat_pos}")

    def set_preferred_lane(self, lane_name: str):
        """Definir lane preferida"""
        lane_map = {
            'top': Lane.TOP,
            'mid': Lane.MID,
            'bot': Lane.BOT,
            'jungle': Lane.JUNGLE
        }

        if lane_name.lower() in lane_map:
            lane_system.set_preferred_lane(lane_map[lane_name.lower()])
            logger.info(f"🎯 Lane preferida definida: {lane_name}")
        else:
            logger.warning(f"Lane inválida: {lane_name}")

    def enable_aggressive_mode(self):
        """Ativar modo agressivo"""
        combat_system.set_aggressive_mode(True)
        self.move_interval = 1.0  # Movimento mais rápido
        logger.info("🔥 Modo agressivo ativado!")

    def enable_defensive_mode(self):
        """Ativar modo defensivo"""
        combat_system.set_aggressive_mode(False)
        self.move_interval = 2.0  # Movimento mais lento
        logger.info("🛡️ Modo defensivo ativado!")
    
    def set_movement_interval(self, seconds: float):
        """Definir intervalo entre movimentos"""
        self.move_interval = seconds
        logger.info(f"Intervalo de movimento definido para {seconds}s")
    
    def set_movement_radius(self, radius: int):
        """Definir raio de movimento"""
        self.movement_radius = radius
        logger.info(f"Raio de movimento definido para {radius}px")
    
    def get_status(self) -> dict:
        """Obter status do movimento automático"""
        return {
            'is_moving': self.is_moving,
            'pattern': self.movement_pattern,
            'current_target': self.current_target,
            'move_interval': self.move_interval,
            'movement_radius': self.movement_radius,
            'last_move_time': self.last_move_time
        }

# Instância global
auto_movement = AutoMovement()
