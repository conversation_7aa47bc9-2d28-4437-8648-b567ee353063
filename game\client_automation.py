"""
Automação do cliente League of Legends - Versão Simplificada
"""
import time
import random

from utils.logger import logger
from utils.input_utils import input_controller
from game.game_detector import game_detector, GameState
from config import coords, champions

class ClientAutomation:
    """Classe para automatizar navegação no cliente LoL"""

    def __init__(self):
        self.selected_champion = champions.DEFAULT_CHAMPION
        self.game_mode = "intro_bots"
        
    def start_game_session(self) -> bool:
        """
        Iniciar uma sessão completa de jogo
        
        Returns:
            True se a sessão foi iniciada com sucesso
        """
        try:
            logger.info("Iniciando sessão de jogo...")
            logger.info("FLUXO COMPLETO - Todas as coordenadas")

            # Aguardar um pouco para estabilizar
            time.sleep(2)

            # Passo 1: Clicar no botão JOGAR
            logger.info(f"1️⃣ Clicando no botão JOGAR: {coords.PLAY_BUTTON}")
            if not self._navigate_to_play():
                logger.error("❌ Falha ao clicar no botão JOGAR")
                return False

            # Passo 2: Selecionar modo BOTS
            logger.info(f"2️⃣ Selecionando modo BOTS: {coords.BOTS_MODE_BUTTON}")
            if not self._select_bots_mode():
                logger.error("❌ Falha ao selecionar modo BOTS")
                return False

            # Passo 3: Confirmar modo de jogo
            logger.info(f"3️⃣ Confirmando modo: {coords.CONFIRM_MODE_BUTTON}")
            if not self._confirm_game_mode():
                logger.error("❌ Falha ao confirmar modo")
                return False

            # Passo 4: Encontrar partida
            logger.info(f"4️⃣ Encontrando partida: {coords.FIND_MATCH_BUTTON}")
            if not self._find_match():
                logger.error("❌ Falha ao encontrar partida")
                return False

            # Passo 5: Aceitar partida
            logger.info(f"5️⃣ Aguardando e aceitando partida...")
            if not self._accept_match():
                logger.error("❌ Falha ao aceitar partida")
                return False

            # Passo 6: Selecionar campeão
            logger.info(f"6️⃣ Selecionando campeão: {self.selected_champion}")
            if not self._select_champion():
                logger.error("❌ Falha ao selecionar campeão")
                return False

            logger.info("✅ Sessão de jogo iniciada com sucesso!")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao iniciar sessão de jogo: {e}")
            return False
    
    def _wait_for_client_ready(self, timeout: int = 60) -> bool:
        """Aguardar cliente estar pronto - Versão simplificada"""
        logger.info("Cliente considerado pronto (versão simplificada)")
        return True
    
    def _navigate_to_play(self) -> bool:
        """Navegar para o botão PLAY - Coordenada manual confirmada"""
        try:
            logger.info("Clicando no botão JOGAR...")
            logger.info(f"Coordenada: {coords.PLAY_BUTTON}")

            # Clicar diretamente na coordenada confirmada
            input_controller.click_at(coords.PLAY_BUTTON[0], coords.PLAY_BUTTON[1])
            time.sleep(3)

            logger.info("Clique no botão JOGAR realizado com sucesso")
            return True

        except Exception as e:
            logger.error(f"Erro ao clicar no botão JOGAR: {e}")
            return False
    
    def _select_bots_mode(self) -> bool:
        """Selecionar modo BOTS"""
        try:
            time.sleep(3)  # Aguardar menu carregar
            input_controller.click_at(coords.BOTS_MODE_BUTTON[0], coords.BOTS_MODE_BUTTON[1])
            logger.info("✅ Modo BOTS selecionado")
            return True
        except Exception as e:
            logger.error(f"Erro ao selecionar modo BOTS: {e}")
            return False

    def _confirm_game_mode(self) -> bool:
        """Confirmar modo de jogo"""
        try:
            time.sleep(2)
            input_controller.click_at(coords.CONFIRM_MODE_BUTTON[0], coords.CONFIRM_MODE_BUTTON[1])
            logger.info("✅ Modo de jogo confirmado")
            return True
        except Exception as e:
            logger.error(f"Erro ao confirmar modo: {e}")
            return False

    def _find_match(self) -> bool:
        """Encontrar partida"""
        try:
            time.sleep(2)
            input_controller.click_at(coords.FIND_MATCH_BUTTON[0], coords.FIND_MATCH_BUTTON[1])
            logger.info("✅ Procurando partida...")
            return True
        except Exception as e:
            logger.error(f"Erro ao encontrar partida: {e}")
            return False

    def _accept_match(self) -> bool:
        """Aceitar partida quando encontrada"""
        try:
            logger.info("Aguardando partida ser encontrada...")

            # Aguardar até 60 segundos pela partida
            for attempt in range(60):
                time.sleep(1)

                # Tentar clicar em aceitar (pode não estar visível ainda)
                try:
                    input_controller.click_at(coords.ACCEPT_BUTTON[0], coords.ACCEPT_BUTTON[1])
                    logger.info("✅ Partida aceita!")
                    return True
                except:
                    continue

            logger.warning("Timeout aguardando partida")
            return False

        except Exception as e:
            logger.error(f"Erro ao aceitar partida: {e}")
            return False

    def _select_champion(self) -> bool:
        """Selecionar campeão"""
        try:
            time.sleep(5)  # Aguardar tela de seleção carregar

            # Clicar na caixa de pesquisa
            input_controller.click_at(coords.CHAMPION_SEARCH_BOX[0], coords.CHAMPION_SEARCH_BOX[1])
            time.sleep(1)

            # Limpar e digitar nome do campeão
            input_controller.clear_text()
            input_controller.type_text(self.selected_champion)
            time.sleep(2)

            # Clicar no primeiro resultado (campeão encontrado)
            input_controller.click_at(coords.CHAMPION_SEARCH_BOX[0], coords.CHAMPION_SEARCH_BOX[1] + 50)
            time.sleep(1)

            # Confirmar campeão
            input_controller.click_at(coords.CONFIRM_CHAMPION_BUTTON[0], coords.CONFIRM_CHAMPION_BUTTON[1])

            logger.info(f"✅ Campeão {self.selected_champion} selecionado!")
            return True

        except Exception as e:
            logger.error(f"Erro ao selecionar campeão: {e}")
            return False
    
    def _confirm_and_queue(self) -> bool:
        """Confirmar seleções e entrar na fila"""
        try:
            logger.info("Confirmando e entrando na fila...")
            
            # Procurar e clicar no botão de confirmação
            # Coordenadas podem variar, ajustar conforme necessário
            confirm_button_coords = (960, 800)  # Posição típica do botão confirmar
            
            input_controller.click_at(confirm_button_coords[0], confirm_button_coords[1])
            time.sleep(3)
            
            # Aguardar entrar na seleção de campeão
            return game_detector.wait_for_state(GameState.CHAMPION_SELECT, timeout=30)
            
        except Exception as e:
            logger.error(f"Erro ao confirmar e entrar na fila: {e}")
            return False
    
    def _handle_champion_select(self) -> bool:
        """Lidar com a seleção de campeão"""
        try:
            logger.info("Iniciando seleção de campeão...")
            
            # Aguardar estar na seleção de campeão
            if not game_detector.wait_for_state(GameState.CHAMPION_SELECT, timeout=10):
                logger.error("Não conseguiu entrar na seleção de campeão")
                return False
            
            # Selecionar campeão
            if not self._select_champion():
                return False
            
            # Aguardar carregamento do jogo
            return game_detector.wait_for_state(GameState.LOADING_SCREEN, timeout=30)
            
        except Exception as e:
            logger.error(f"Erro na seleção de campeão: {e}")
            return False
    
    def _select_champion(self) -> bool:
        """Selecionar campeão específico"""
        try:
            logger.info(f"Selecionando campeão: {self.selected_champion}")
            
            # Clicar na barra de busca
            input_controller.click_at(coords.CHAMPION_SEARCH[0], coords.CHAMPION_SEARCH[1])
            time.sleep(0.5)
            
            # Limpar campo de busca
            input_controller.key_combination('ctrl', 'a')
            time.sleep(0.2)
            
            # Digitar nome do campeão
            input_controller.type_text(self.selected_champion)
            time.sleep(1)
            
            # Clicar no campeão (primeira opção da busca)
            champion_position = (500, 400)  # Posição típica do primeiro resultado
            input_controller.click_at(champion_position[0], champion_position[1])
            time.sleep(1)
            
            # Clicar em LOCK IN
            input_controller.click_at(coords.LOCK_IN_BUTTON[0], coords.LOCK_IN_BUTTON[1])
            time.sleep(2)
            
            logger.info(f"Campeão {self.selected_champion} selecionado!")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao selecionar campeão: {e}")
            return False
    
    def _handle_reconnect(self) -> bool:
        """Lidar com tela de reconexão"""
        try:
            logger.info("Tentando reconectar...")
            
            # Clicar no botão Reconnect
            reconnect_coords = (960, 540)  # Posição típica do botão
            input_controller.click_at(reconnect_coords[0], reconnect_coords[1])
            
            # Aguardar reconexão
            time.sleep(10)
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao reconectar: {e}")
            return False
    
    def handle_post_game(self) -> bool:
        """Lidar com tela pós-jogo"""
        try:
            logger.info("Lidando com tela pós-jogo...")
            
            # Aguardar tela pós-jogo aparecer
            if not game_detector.wait_for_state(GameState.POST_GAME, timeout=30):
                logger.warning("Não detectou tela pós-jogo")
                return False
            
            # Aguardar um pouco para carregar completamente
            time.sleep(5)
            
            # Clicar em Continue ou Play Again
            continue_coords = (960, 950)  # Posição típica do botão
            input_controller.click_at(continue_coords[0], continue_coords[1])
            time.sleep(3)
            
            # Aguardar voltar ao menu principal
            return game_detector.wait_for_state(GameState.CLIENT_MAIN_MENU, timeout=30)
            
        except Exception as e:
            logger.error(f"Erro ao lidar com pós-jogo: {e}")
            return False
    
    def set_champion(self, champion_name: str):
        """Definir campeão a ser selecionado"""
        if champion_name in champions.EASY_CHAMPIONS:
            self.selected_champion = champion_name
            logger.info(f"Campeão definido para: {champion_name}")
        else:
            logger.warning(f"Campeão {champion_name} não está na lista de campeões fáceis")
    
    def get_random_champion(self) -> str:
        """Obter campeão aleatório da lista de campeões fáceis"""
        return random.choice(champions.EASY_CHAMPIONS)
    
    def cycle_champions(self):
        """Alternar entre campeões para variar"""
        current_index = champions.EASY_CHAMPIONS.index(self.selected_champion)
        next_index = (current_index + 1) % len(champions.EASY_CHAMPIONS)
        self.selected_champion = champions.EASY_CHAMPIONS[next_index]
        logger.info(f"Alternando para campeão: {self.selected_champion}")



# Instância global
client_automation = ClientAutomation()
