"""
Analisar screenshot do cliente LoL para encontrar botão JOGAR
"""
import cv2
import numpy as np
from PIL import Image
import os

def analyze_client_screenshot():
    """Analisar screenshot do cliente para encontrar botão JOGAR"""
    
    # Verificar se arquivo existe
    screenshot_path = "screenshots/client.png"
    if not os.path.exists(screenshot_path):
        print(f"❌ Arquivo não encontrado: {screenshot_path}")
        return None
    
    print(f"📸 Analisando: {screenshot_path}")
    
    try:
        # Carregar imagem
        image = cv2.imread(screenshot_path)
        if image is None:
            print("❌ Erro ao carregar imagem")
            return None
        
        height, width = image.shape[:2]
        print(f"📏 Resolução da imagem: {width}x{height}")
        
        # Converter para diferentes espaços de cor para análise
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Procurar por botão dourado/amarelo (típico do botão JOGAR)
        print("🔍 Procurando botão dourado/amarelo...")
        
        # Máscara para cor dourada/amarela
        lower_gold = np.array([15, 100, 100])
        upper_gold = np.array([35, 255, 255])
        mask_gold = cv2.inRange(hsv, lower_gold, upper_gold)
        
        # Máscara para cor azul (outros botões)
        lower_blue = np.array([100, 100, 100])
        upper_blue = np.array([130, 255, 255])
        mask_blue = cv2.inRange(hsv, lower_blue, upper_blue)
        
        # Encontrar contornos dourados
        contours_gold, _ = cv2.findContours(mask_gold, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contours_blue, _ = cv2.findContours(mask_blue, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        print(f"🟡 Encontrados {len(contours_gold)} contornos dourados")
        print(f"🔵 Encontrados {len(contours_blue)} contornos azuis")
        
        # Analisar contornos dourados (possível botão JOGAR)
        jogar_candidates = []
        for i, contour in enumerate(contours_gold):
            area = cv2.contourArea(contour)
            if area > 1000:  # Filtrar contornos muito pequenos
                # Calcular centro
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # Calcular bounding box
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    jogar_candidates.append({
                        'center': (cx, cy),
                        'area': area,
                        'bbox': (x, y, w, h),
                        'aspect_ratio': w/h if h > 0 else 0
                    })
                    
                    print(f"🎯 Candidato JOGAR {i+1}:")
                    print(f"   Centro: ({cx}, {cy})")
                    print(f"   Área: {area}")
                    print(f"   Tamanho: {w}x{h}")
                    print(f"   Proporção: {w/h:.2f}")
        
        # Procurar por regiões com texto (método alternativo)
        print("\n📝 Procurando regiões de texto...")
        
        # Aplicar threshold para destacar texto
        _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        
        # Encontrar contornos de texto
        contours_text, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        text_candidates = []
        for contour in contours_text:
            area = cv2.contourArea(contour)
            if 500 < area < 10000:  # Filtrar por tamanho
                x, y, w, h = cv2.boundingRect(contour)
                
                # Filtrar por proporção (botões geralmente são mais largos que altos)
                if w > h and w > 50:
                    cx = x + w//2
                    cy = y + h//2
                    text_candidates.append({
                        'center': (cx, cy),
                        'area': area,
                        'bbox': (x, y, w, h),
                        'aspect_ratio': w/h
                    })
        
        print(f"📝 Encontradas {len(text_candidates)} regiões de texto")
        
        # Criar imagem com marcações
        debug_img = image.copy()
        
        # Marcar candidatos dourados em vermelho
        for i, candidate in enumerate(jogar_candidates):
            cx, cy = candidate['center']
            x, y, w, h = candidate['bbox']
            
            # Desenhar retângulo
            cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 0, 255), 3)
            
            # Desenhar centro
            cv2.circle(debug_img, (cx, cy), 10, (0, 0, 255), -1)
            
            # Adicionar texto
            cv2.putText(debug_img, f"JOGAR?{i+1}({cx},{cy})", (cx-60, cy-20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Marcar candidatos de texto em verde
        for i, candidate in enumerate(text_candidates[:10]):  # Limitar a 10
            cx, cy = candidate['center']
            x, y, w, h = candidate['bbox']
            
            cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 255, 0), 2)
            cv2.putText(debug_img, f"T{i+1}", (cx-10, cy+5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        
        # Salvar imagens de análise
        cv2.imwrite("screenshots/analysis_marked.png", debug_img)
        cv2.imwrite("screenshots/mask_gold.png", mask_gold)
        cv2.imwrite("screenshots/mask_blue.png", mask_blue)
        cv2.imwrite("screenshots/text_thresh.png", thresh)
        
        print("\n💾 Arquivos salvos:")
        print("   - screenshots/analysis_marked.png (imagem com marcações)")
        print("   - screenshots/mask_gold.png (máscara dourada)")
        print("   - screenshots/mask_blue.png (máscara azul)")
        print("   - screenshots/text_thresh.png (texto destacado)")
        
        # Determinar melhor candidato para botão JOGAR
        if jogar_candidates:
            # Ordenar por área (botão JOGAR geralmente é grande)
            jogar_candidates.sort(key=lambda x: x['area'], reverse=True)
            
            best_candidate = jogar_candidates[0]
            print(f"\n🎯 MELHOR CANDIDATO PARA BOTÃO JOGAR:")
            print(f"   Coordenadas: {best_candidate['center']}")
            print(f"   Área: {best_candidate['area']}")
            print(f"   Tamanho: {best_candidate['bbox'][2]}x{best_candidate['bbox'][3]}")
            
            return best_candidate['center']
        
        else:
            print("\n❌ Nenhum candidato dourado encontrado")
            
            # Tentar com regiões de texto no centro da tela
            center_candidates = [
                c for c in text_candidates 
                if abs(c['center'][0] - width//2) < width//4 and 
                   abs(c['center'][1] - height//2) < height//4
            ]
            
            if center_candidates:
                center_candidates.sort(key=lambda x: x['area'], reverse=True)
                best_text = center_candidates[0]
                print(f"\n📝 CANDIDATO DE TEXTO NO CENTRO:")
                print(f"   Coordenadas: {best_text['center']}")
                return best_text['center']
            
            print("❌ Nenhum candidato encontrado")
            return None
    
    except Exception as e:
        print(f"❌ Erro na análise: {e}")
        return None

def suggest_coordinates(jogar_position, image_width, image_height):
    """Sugerir coordenadas baseadas na posição encontrada"""
    
    if not jogar_position:
        print("\n💡 SUGESTÕES BASEADAS EM RESOLUÇÃO:")
        print(f"   Resolução detectada: {image_width}x{image_height}")
        
        if image_width == 1920 and image_height == 1080:
            print("   Coordenadas sugeridas para 1920x1080:")
            print("   PLAY_BUTTON = (960, 580)")
        elif image_width == 1366 and image_height == 768:
            print("   Coordenadas sugeridas para 1366x768:")
            print("   PLAY_BUTTON = (683, 414)")
        else:
            # Calcular proporcionalmente
            center_x = image_width // 2
            center_y = int(image_height * 0.54)  # 54% da altura
            print(f"   Coordenadas proporcionais:")
            print(f"   PLAY_BUTTON = ({center_x}, {center_y})")
        
        return
    
    x, y = jogar_position
    print(f"\n✅ COORDENADAS ENCONTRADAS PARA BOTÃO JOGAR:")
    print(f"   PLAY_BUTTON = ({x}, {y})")
    
    # Sugerir outras coordenadas baseadas na posição do JOGAR
    print(f"\n💡 OUTRAS COORDENADAS SUGERIDAS:")
    
    # Training geralmente fica à esquerda
    training_x = max(50, x - 400)
    training_y = max(50, y - 200)
    print(f"   TRAINING_BUTTON = ({training_x}, {training_y})")
    
    # VS AI no centro-esquerda
    vs_ai_x = max(200, x - 200)
    vs_ai_y = max(100, y - 100)
    print(f"   VS_AI_BUTTON = ({vs_ai_x}, {vs_ai_y})")
    
    # Intro Bots no centro-direita
    intro_x = min(image_width - 50, x + 200)
    intro_y = max(200, y - 50)
    print(f"   INTRO_BOTS_BUTTON = ({intro_x}, {intro_y})")
    
    # Confirm button embaixo
    confirm_x = x
    confirm_y = min(image_height - 50, y + 200)
    print(f"   CONFIRM_BUTTON = ({confirm_x}, {confirm_y})")

def main():
    """Função principal"""
    print("🔍 Análise de Screenshot - Botão JOGAR")
    print("=" * 40)
    
    # Verificar se pasta existe
    if not os.path.exists("screenshots"):
        print("❌ Pasta 'screenshots' não encontrada!")
        return
    
    # Analisar screenshot
    jogar_position = analyze_client_screenshot()
    
    # Carregar imagem para obter dimensões
    try:
        image = cv2.imread("screenshots/client.png")
        if image is not None:
            height, width = image.shape[:2]
            suggest_coordinates(jogar_position, width, height)
        
    except Exception as e:
        print(f"Erro ao obter dimensões: {e}")
    
    print("\n" + "=" * 40)
    print("✅ Análise concluída!")
    print("Verifique os arquivos gerados na pasta screenshots/")

if __name__ == "__main__":
    main()
