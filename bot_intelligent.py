"""
Bot inteligente com combate, lanes e detecção avançada
"""
import time
import signal
import sys
from game.lol_detector import lol_detector
from game.visual_detection import visual_detector
from game.auto_movement import auto_movement
from game.combat_system import combat_system
from game.lane_system import lane_system, Lane
from game.minimap_system import minimap_system
from game.advanced_minimap import advanced_minimap, GameState, UnitType
from utils.logger import logger

class IntelligentBot:
    """Bot inteligente com sistemas avançados"""
    
    def __init__(self):
        self.running = False
        self.paused = False
        
        # Estatísticas
        self.stats = {
            'enemies_attacked': 0,
            'minions_farmed': 0,
            'lane_changes': 0,
            'retreats': 0,
            'total_actions': 0,
            'minimap_attacks': 0,
            'visual_attacks': 0,
            'strategic_actions': 0,
            'wave_retreats': 0
        }
        
        # Configurar handler para Ctrl+C
        signal.signal(signal.SIGINT, self._signal_handler)
        
        logger.info("🤖 Bot Inteligente LoL inicializado")
    
    def _signal_handler(self, signum, frame):
        """Handler para parar o bot com Ctrl+C"""
        logger.info("\n⏸️ Parando bot inteligente...")
        self.running = False
        auto_movement.stop_auto_movement()
        self._show_final_stats()
        sys.exit(0)
    
    def configure_bot(self):
        """Configurar bot antes de iniciar"""
        print("\n⚙️ CONFIGURAÇÃO DO BOT INTELIGENTE")
        print("=" * 40)
        
        # Escolher lane preferida
        print("\n🎯 Escolha sua lane preferida:")
        print("1. Top Lane")
        print("2. Mid Lane (Recomendado)")
        print("3. Bot Lane")
        print("4. Jungle")
        
        lane_choice = input("Escolha (1-4, Enter para Mid): ").strip()
        
        lane_map = {
            '1': 'top',
            '2': 'mid',
            '3': 'bot',
            '4': 'jungle',
            '': 'mid'  # Padrão
        }
        
        selected_lane = lane_map.get(lane_choice, 'mid')
        auto_movement.set_preferred_lane(selected_lane)
        
        # Escolher estilo de jogo
        print(f"\n🎮 Estilo de jogo:")
        print("1. Agressivo (ataca mais, arrisca mais)")
        print("2. Defensivo (foca em farm, mais seguro)")
        print("3. Balanceado (recomendado)")
        
        style_choice = input("Escolha (1-3, Enter para Balanceado): ").strip()
        
        if style_choice == '1':
            auto_movement.enable_aggressive_mode()
            auto_movement.set_movement_interval(1.5)
            print("🔥 Modo agressivo configurado!")
        elif style_choice == '2':
            auto_movement.enable_defensive_mode()
            auto_movement.set_movement_interval(3.0)
            print("🛡️ Modo defensivo configurado!")
        else:
            auto_movement.set_movement_interval(2.0)
            print("⚖️ Modo balanceado configurado!")
        
        print(f"\n✅ Configuração concluída:")
        print(f"   🎯 Lane: {selected_lane.upper()}")
        print(f"   🎮 Estilo: {['Balanceado', 'Agressivo', 'Defensivo'][int(style_choice) - 1] if style_choice in ['1', '2', '3'] else 'Balanceado'}")
    
    def wait_for_game_ready(self) -> bool:
        """Aguardar jogo estar pronto"""
        logger.info("🎮 Aguardando League of Legends...")
        
        print("\n📋 INSTRUÇÕES:")
        print("1. ✅ Abra o League of Legends")
        print("2. ✅ Entre em uma partida")
        print("3. ✅ Configure em TELA CHEIA (F11)")
        print("4. ✅ Aguarde o bot detectar automaticamente")
        
        input("\n🎯 Pressione Enter quando estiver na partida em tela cheia...")
        
        return lol_detector.wait_for_lol_fullscreen(timeout=180)
    
    def start_intelligent_automation(self):
        """Iniciar automação inteligente"""
        logger.info("🚀 Iniciando automação inteligente!")
        
        # Configurar movimento com sistema de lanes
        auto_movement.start_auto_movement("lane")
        
        self.running = True
        
        # Estatísticas
        frame_count = 0
        last_status_time = time.time()
        start_time = time.time()
        
        logger.info("🧠 Bot inteligente ativo!")
        logger.info("   🎯 Combate automático: ATIVO")
        logger.info("   🗺️ Sistema de lanes: ATIVO")
        logger.info("   👁️ Detecção visual: ATIVA")
        logger.info("   🗺️ Sistema de minimapa: ATIVO")
        logger.info("   🌊 Sistema avançado de waves: ATIVO")
        
        try:
            while self.running:
                frame_count += 1
                current_time = time.time()
                
                # Verificar status do jogo
                if not self._check_game_status():
                    continue
                
                # Detectar alvos visuais
                targets = visual_detector.detect_targets()
                enemies = [t for t in targets if t.type == 'enemy_champion']
                minions = [t for t in targets if t.type == 'enemy_minion']
                low_health = visual_detector.is_low_health()

                # Detectar alvos do minimapa
                minimap_enemies = minimap_system.get_priority_targets()
                minimap_champions = [e for e in minimap_enemies if e.is_champion]

                # Sistema avançado de minimapa
                advanced_units = []
                try:
                    minimap_img = advanced_minimap.capture_minimap()
                    if minimap_img is not None:
                        advanced_units = advanced_minimap.detect_units(minimap_img)
                except Exception as e:
                    logger.debug(f"Erro no sistema avançado: {e}")

                # Tomar decisões inteligentes
                action_taken = self._make_intelligent_decision(enemies, minions, low_health, minimap_enemies, advanced_units)
                
                # Atualizar movimento (inclui combate automático)
                auto_movement.update_movement()
                
                # Mostrar status detalhado a cada 15 segundos
                if current_time - last_status_time >= 15:
                    self._show_detailed_status(frame_count, start_time, enemies, minions, low_health, minimap_enemies, advanced_units)
                    last_status_time = current_time

                # Status resumido
                self._show_brief_status(frame_count, enemies, minions, low_health, minimap_enemies, advanced_units)
                
                time.sleep(0.25)  # 4 FPS
                
        except KeyboardInterrupt:
            logger.info("\n⏸️ Bot interrompido pelo usuário")
        except Exception as e:
            logger.error(f"❌ Erro na automação: {e}")
        finally:
            self._cleanup()
    
    def _check_game_status(self) -> bool:
        """Verificar status do jogo"""
        try:
            if not lol_detector.is_lol_running():
                if not self.paused:
                    logger.warning("⚠️ LoL não está rodando - pausando...")
                    self.paused = True
                return False
            
            if not lol_detector.is_fullscreen():
                if not self.paused:
                    logger.warning("⚠️ Jogo não está em tela cheia - pausando...")
                    self.paused = True
                return False
            
            if self.paused:
                logger.info("✅ Jogo voltou ao normal - retomando")
                self.paused = False
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao verificar status: {e}")
            return False
    
    def _make_intelligent_decision(self, enemies, minions, low_health, minimap_enemies, advanced_units) -> bool:
        """Tomar decisões inteligentes baseadas na situação"""
        try:
            # PRIORIDADE 1: Sistema avançado de waves
            if advanced_units:
                ally_minions = [u for u in advanced_units if u.unit_type == UnitType.ALLY_MINION]
                enemy_champions = [u for u in advanced_units if u.unit_type == UnitType.ENEMY_CHAMPION]

                # Verificar se deve recuar por poucos minions
                if len(ally_minions) <= 2:
                    logger.warning("🌊 POUCOS MINIONS ALIADOS - Recuando para aguardar wave!")
                    auto_movement.retreat_to_tower()
                    self.stats['wave_retreats'] += 1
                    self.stats['total_actions'] += 1
                    return True

                # Priorizar campeões inimigos se temos minions aliados
                if enemy_champions and len(ally_minions) >= 3:
                    logger.info("⚔️ CAMPEÃO INIMIGO COM SUPORTE DE MINIONS!")
                    self.stats['strategic_actions'] += 1
                    self.stats['enemies_attacked'] += 1
                    self.stats['total_actions'] += 1
                    return True

            # PRIORIDADE 2: Vida baixa - recuar para torre
            if low_health:
                logger.warning("🔴 VIDA BAIXA - Recuando para torre!")
                auto_movement.retreat_to_tower()
                self.stats['retreats'] += 1
                self.stats['total_actions'] += 1
                return True
            
            # PRIORIDADE 3: Inimigos do minimapa (campeões prioritários)
            minimap_champions = [e for e in minimap_enemies if e.is_champion]
            if minimap_champions:
                nearest_champion = minimap_champions[0]  # Já ordenado por prioridade
                logger.info(f"🗺️ CAMPEÃO DETECTADO NO MINIMAPA ({nearest_champion.lane.value})!")
                self.stats['minimap_attacks'] += 1
                self.stats['enemies_attacked'] += 1
                self.stats['total_actions'] += 1
                return True

            # PRIORIDADE 4: Inimigos visuais próximos
            if enemies:
                nearest = visual_detector.get_nearest_enemy()
                if nearest:
                    distance = ((nearest.position[0] - 960)**2 + (nearest.position[1] - 540)**2)**0.5
                    if distance < 100:
                        logger.warning(f"👁️ INIMIGO VISUAL PRÓXIMO ({distance:.0f}px)!")
                        self.stats['visual_attacks'] += 1
                        self.stats['enemies_attacked'] += 1
                        self.stats['total_actions'] += 1
                        return True
            
            # PRIORIDADE 5: Farm inteligente
            if minions and len(minions) >= 3:
                logger.debug(f"🌾 {len(minions)} minions disponíveis para farm")
                # Sistema de movimento já cuida do farm
                self.stats['minions_farmed'] += 1
            
            return False
            
        except Exception as e:
            logger.error(f"Erro na decisão inteligente: {e}")
            return False
    
    def _show_detailed_status(self, frames, start_time, enemies, minions, low_health, minimap_enemies, advanced_units):
        """Mostrar status detalhado"""
        elapsed = time.time() - start_time
        fps = frames / elapsed if elapsed > 0 else 0
        
        logger.info("📊 STATUS DETALHADO DO BOT INTELIGENTE:")
        logger.info(f"   ⏱️ Tempo ativo: {elapsed:.0f}s ({elapsed/60:.1f}min)")
        logger.info(f"   🖼️ Frames: {frames} ({fps:.1f} FPS)")
        logger.info(f"   🎯 Ações totais: {self.stats['total_actions']}")
        logger.info(f"   ⚔️ Inimigos atacados: {self.stats['enemies_attacked']}")
        logger.info(f"   🗺️ Ataques do minimapa: {self.stats['minimap_attacks']}")
        logger.info(f"   👁️ Ataques visuais: {self.stats['visual_attacks']}")
        logger.info(f"   🌊 Ações estratégicas: {self.stats['strategic_actions']}")
        logger.info(f"   🌾 Minions farmados: {self.stats['minions_farmed']}")
        logger.info(f"   🏃 Recuos: {self.stats['retreats']}")
        logger.info(f"   🌊 Recuos por wave: {self.stats['wave_retreats']}")
        
        # Status dos sistemas
        lane_status = lane_system.get_current_status()
        combat_status = combat_system.get_combat_status()
        
        logger.info(f"   🗺️ Lane atual: {lane_status['current_lane']} - {lane_status['current_position']}")
        logger.info(f"   🎯 Alvo atual: {combat_status.get('current_target', 'Nenhum')}")
        logger.info(f"   👁️ Inimigos visuais: {len(enemies)}")
        logger.info(f"   ⚪ Minions visuais: {len(minions)}")
        logger.info(f"   🗺️ Inimigos no mapa: {len(minimap_enemies)}")
        logger.info(f"   👤 Campeões no mapa: {len([e for e in minimap_enemies if e.is_champion])}")

        # Status do sistema avançado
        if advanced_units:
            ally_minions = [u for u in advanced_units if u.unit_type == UnitType.ALLY_MINION]
            enemy_champions = [u for u in advanced_units if u.unit_type == UnitType.ENEMY_CHAMPION]
            towers = [u for u in advanced_units if u.unit_type == UnitType.TOWER]

            logger.info(f"   🔵 Minions aliados: {len(ally_minions)}")
            logger.info(f"   ⚔️ Campeões inimigos: {len(enemy_champions)}")
            logger.info(f"   🏗️ Torres detectadas: {len(towers)}")
            logger.info(f"   🌊 Estado do jogo: {advanced_minimap.current_state.value}")

        logger.info(f"   🔴 Vida baixa: {'SIM' if low_health else 'NÃO'}")
    
    def _show_brief_status(self, frames, enemies, minions, low_health, minimap_enemies, advanced_units):
        """Status resumido na mesma linha"""
        minimap_champions = len([e for e in minimap_enemies if e.is_champion])

        # Status do sistema avançado
        ally_minions_count = 0
        enemy_champions_count = 0
        game_state = "UNKNOWN"

        if advanced_units:
            ally_minions_count = len([u for u in advanced_units if u.unit_type == UnitType.ALLY_MINION])
            enemy_champions_count = len([u for u in advanced_units if u.unit_type == UnitType.ENEMY_CHAMPION])
            game_state = advanced_minimap.current_state.value[:8]  # Primeiros 8 caracteres

        print(f"\r🤖 Frame {frames:4d} | "
              f"👁️ {len(enemies):2d} | ⚪ {len(minions):2d} | "
              f"🗺️ {len(minimap_enemies):2d} | 👤 {minimap_champions:2d} | "
              f"🔵 {ally_minions_count:2d} | ⚔️ {enemy_champions_count:2d} | "
              f"🌊 {game_state:8s} | "
              f"🔴 {'SIM' if low_health else 'NÃO':3s} | "
              f"Ações: {self.stats['total_actions']:3d} | "
              f"{'⚠️ PAUSADO' if self.paused else '🧠 INTELIGENTE'}",
              end="", flush=True)
    
    def _show_final_stats(self):
        """Mostrar estatísticas finais"""
        logger.info("\n📊 ESTATÍSTICAS FINAIS:")
        logger.info(f"   🎯 Ações totais: {self.stats['total_actions']}")
        logger.info(f"   ⚔️ Inimigos atacados: {self.stats['enemies_attacked']}")
        logger.info(f"   🗺️ Ataques do minimapa: {self.stats['minimap_attacks']}")
        logger.info(f"   👁️ Ataques visuais: {self.stats['visual_attacks']}")
        logger.info(f"   🌊 Ações estratégicas: {self.stats['strategic_actions']}")
        logger.info(f"   🌾 Minions farmados: {self.stats['minions_farmed']}")
        logger.info(f"   🏃 Recuos executados: {self.stats['retreats']}")
        logger.info(f"   🌊 Recuos por wave: {self.stats['wave_retreats']}")
    
    def _cleanup(self):
        """Limpeza final"""
        auto_movement.stop_auto_movement()
        logger.info("\n🏁 Bot inteligente finalizado!")

def main():
    """Função principal"""
    try:
        bot = IntelligentBot()
        
        # Configurar bot
        bot.configure_bot()
        
        # Aguardar jogo
        if not bot.wait_for_game_ready():
            logger.error("❌ Não foi possível iniciar o bot")
            return
        
        # Iniciar automação inteligente
        bot.start_intelligent_automation()
        
    except KeyboardInterrupt:
        logger.info("\n👋 Bot interrompido")
    except Exception as e:
        logger.error(f"❌ Erro crítico: {e}")
    finally:
        input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
