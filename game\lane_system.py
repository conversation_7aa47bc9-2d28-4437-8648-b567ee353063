"""
Sistema de lanes para League of Legends
Gerencia movimento e posicionamento nas lanes
"""
import time
import random
from typing import Tuple, List, Optional
from enum import Enum
from dataclasses import dataclass
from utils.logger import logger


class Lane(Enum):
    """Lanes do jogo"""
    TOP = "top"
    MID = "mid"
    BOT = "bot"
    JUNGLE = "jungle"


@dataclass
class LanePosition:
    """Posição específica em uma lane"""
    x: int
    y: int
    lane: Lane
    position_type: str  # "safe", "farm", "aggressive", "retreat"
    description: str


class LaneSystem:
    """Sistema de gerenciamento de lanes"""
    
    def __init__(self):
        # Lane atual
        self.current_lane = Lane.MID
        self.preferred_lane = Lane.MID
        
        # Posições para cada lane (coordenadas mais realistas para LoL)
        self.lane_positions = {
            Lane.TOP: {
                'safe': LanePosition(800, 200, Lane.TOP, "safe", "Posição segura top"),
                'farm': LanePosition(900, 250, Lane.TOP, "farm", "Posição de farm top"),
                'aggressive': LanePosition(1000, 300, Lane.TOP, "aggressive", "Posição agressiva top"),
                'retreat': LanePosition(700, 150, Lane.TOP, "retreat", "Recuo para torre top")
            },
            Lane.MID: {
                'safe': LanePosition(960, 400, Lane.MID, "safe", "Posição segura mid"),
                'farm': LanePosition(1100, 500, Lane.MID, "farm", "Posição de farm mid"),
                'aggressive': LanePosition(1200, 600, Lane.MID, "aggressive", "Posição agressiva mid"),
                'retreat': LanePosition(800, 300, Lane.MID, "retreat", "Recuo para torre mid")
            },
            Lane.BOT: {
                'safe': LanePosition(1200, 600, Lane.BOT, "safe", "Posição segura bot"),
                'farm': LanePosition(1300, 700, Lane.BOT, "farm", "Posição de farm bot"),
                'aggressive': LanePosition(1400, 800, Lane.BOT, "aggressive", "Posição agressiva bot"),
                'retreat': LanePosition(1100, 500, Lane.BOT, "retreat", "Recuo para torre bot")
            },
            Lane.JUNGLE: {
                'safe': LanePosition(600, 400, Lane.JUNGLE, "safe", "Posição segura jungle"),
                'farm': LanePosition(700, 500, Lane.JUNGLE, "farm", "Camp de jungle"),
                'aggressive': LanePosition(800, 600, Lane.JUNGLE, "aggressive", "Gank position"),
                'retreat': LanePosition(500, 300, Lane.JUNGLE, "retreat", "Recuo jungle")
            }
        }
        
        # Estado atual
        self.current_position_type = "safe"
        self.last_movement_time = 0
        self.movement_interval = 3.0  # Mover a cada 3 segundos
        
        # Padrão de movimento mais focado na lane
        self.movement_pattern = ["farm", "farm", "aggressive", "safe"]
        self.pattern_index = 0
        
        logger.info(f"🛣️ Sistema de lanes inicializado - Lane: {self.current_lane.value}")
    
    def set_preferred_lane(self, lane: Lane):
        """Definir lane preferida"""
        self.preferred_lane = lane
        self.current_lane = lane
        self.current_position_type = "safe"  # Começar em posição segura
        self.pattern_index = 0  # Resetar padrão
        self.last_movement_time = 0  # Forçar movimento imediato
        logger.info(f"🎯 LANE PREFERIDA DEFINIDA: {lane.value.upper()}")
        logger.info(f"📍 Posições da lane {lane.value}: {list(self.lane_positions[lane].keys())}")
    
    def update_lane_movement(self) -> Tuple[int, int]:
        """Atualizar movimento baseado na lane atual"""
        try:
            current_time = time.time()
            
            # Verificar se é hora de mover
            if current_time - self.last_movement_time < self.movement_interval:
                # Retornar posição atual
                current_pos = self.lane_positions[self.current_lane][self.current_position_type]
                return (current_pos.x, current_pos.y)
            
            # Próxima posição no padrão
            self.pattern_index = (self.pattern_index + 1) % len(self.movement_pattern)
            self.current_position_type = self.movement_pattern[self.pattern_index]
            
            # Obter nova posição
            new_position = self.lane_positions[self.current_lane][self.current_position_type]
            
            # Adicionar pequena variação para parecer mais humano
            variation_x = random.randint(-15, 15)
            variation_y = random.randint(-15, 15)

            final_x = max(200, min(1720, new_position.x + variation_x))
            final_y = max(100, min(900, new_position.y + variation_y))
            
            self.last_movement_time = current_time

            logger.info(f"🛣️ MOVIMENTO LANE: {self.current_lane.value.upper()} -> {self.current_position_type.upper()} ({final_x}, {final_y})")

            return (final_x, final_y)
            
        except Exception as e:
            logger.error(f"Erro no movimento de lane: {e}")
            return (960, 540)  # Centro da tela como fallback
    
    def get_farm_position(self) -> Tuple[int, int]:
        """Obter posição ideal para farm"""
        try:
            farm_pos = self.lane_positions[self.current_lane]['farm']
            
            # Adicionar pequena variação
            variation_x = random.randint(-20, 20)
            variation_y = random.randint(-20, 20)
            
            final_x = max(100, min(1820, farm_pos.x + variation_x))
            final_y = max(100, min(980, farm_pos.y + variation_y))
            
            return (final_x, final_y)
            
        except Exception as e:
            logger.error(f"Erro ao obter posição de farm: {e}")
            return (960, 540)
    
    def get_safe_position(self) -> Tuple[int, int]:
        """Obter posição segura"""
        try:
            safe_pos = self.lane_positions[self.current_lane]['safe']
            return (safe_pos.x, safe_pos.y)
            
        except Exception as e:
            logger.error(f"Erro ao obter posição segura: {e}")
            return (960, 540)
    
    def get_retreat_position(self) -> Tuple[int, int]:
        """Obter posição de recuo (próxima à torre)"""
        try:
            retreat_pos = self.lane_positions[self.current_lane]['retreat']
            return (retreat_pos.x, retreat_pos.y)
            
        except Exception as e:
            logger.error(f"Erro ao obter posição de recuo: {e}")
            return (960, 540)
    
    def get_aggressive_position(self) -> Tuple[int, int]:
        """Obter posição agressiva"""
        try:
            aggressive_pos = self.lane_positions[self.current_lane]['aggressive']
            
            # Adicionar variação para ser menos previsível
            variation_x = random.randint(-25, 25)
            variation_y = random.randint(-25, 25)
            
            final_x = max(100, min(1820, aggressive_pos.x + variation_x))
            final_y = max(100, min(980, aggressive_pos.y + variation_y))
            
            return (final_x, final_y)
            
        except Exception as e:
            logger.error(f"Erro ao obter posição agressiva: {e}")
            return (960, 540)
    
    def switch_to_lane(self, lane: Lane):
        """Mudar para uma lane específica"""
        if lane != self.current_lane:
            logger.info(f"🔄 Mudando de lane: {self.current_lane.value} -> {lane.value}")
            self.current_lane = lane
            self.current_position_type = "safe"  # Começar em posição segura
            self.pattern_index = 0
    
    def get_current_status(self) -> dict:
        """Obter status atual do sistema"""
        current_pos = self.lane_positions[self.current_lane][self.current_position_type]
        
        return {
            'current_lane': self.current_lane.value,
            'preferred_lane': self.preferred_lane.value,
            'current_position': self.current_position_type,
            'current_coordinates': (current_pos.x, current_pos.y),
            'pattern_index': self.pattern_index,
            'last_movement': self.last_movement_time
        }
    
    def set_movement_pattern(self, pattern: List[str]):
        """Definir padrão de movimento personalizado"""
        valid_types = ["safe", "farm", "aggressive", "retreat"]
        
        if all(pos_type in valid_types for pos_type in pattern):
            self.movement_pattern = pattern
            self.pattern_index = 0
            logger.info(f"🔄 Padrão de movimento atualizado: {pattern}")
        else:
            logger.warning(f"Padrão inválido: {pattern}")
    
    def set_movement_interval(self, interval: float):
        """Definir intervalo entre movimentos"""
        self.movement_interval = max(1.0, interval)  # Mínimo 1 segundo
        logger.info(f"⏱️ Intervalo de movimento: {self.movement_interval}s")
    
    def force_position_type(self, position_type: str):
        """Forçar um tipo de posição específico"""
        valid_types = ["safe", "farm", "aggressive", "retreat"]
        
        if position_type in valid_types:
            self.current_position_type = position_type
            logger.info(f"🎯 Posição forçada: {position_type}")
        else:
            logger.warning(f"Tipo de posição inválido: {position_type}")


# Instância global
lane_system = LaneSystem()
