"""
Sistema de combate inteligente para League of Legends
Detecta e ataca inimigos, especialmente campeões
Integrado com sistema de minimapa
"""
import time
import math
from typing import List, Tuple, Optional
from dataclasses import dataclass
from utils.logger import logger
from utils.input_utils import input_controller
from game.visual_detection import visual_detector, DetectedTarget
from game.minimap_system import minimap_system, MinimapEnemy
from game.advanced_minimap import advanced_minimap, GameState, UnitType

@dataclass
class CombatTarget:
    """Alvo de combate com informações adicionais"""
    target: DetectedTarget
    distance: float
    priority: int
    last_seen: float
    attack_count: int = 0

class CombatSystem:
    """Sistema de combate inteligente"""
    
    def __init__(self):
        self.current_target = None
        self.target_history = {}
        self.last_attack_time = 0
        self.attack_cooldown = 0.8  # Segundos entre ataques
        self.follow_distance = 200  # Distância para seguir inimigos
        self.attack_distance = 150  # Distância para atacar
        
        # Centro da tela (posição do nosso campeão)
        self.champion_pos = (960, 540)
        
        # Prioridades de alvo
        self.target_priorities = {
            'enemy_champion': 100,  # Prioridade máxima
            'enemy_minion': 30,     # Prioridade baixa
            'low_health': 80        # Alta prioridade se vida baixa
        }
        
        logger.info("Sistema de combate inicializado")
    
    def update_combat(self) -> bool:
        """
        Atualizar sistema de combate avançado
        Returns: True se uma ação de combate foi tomada
        """
        try:
            # PRIORIDADE 1: Sistema avançado de minimapa
            minimap_action = self._process_advanced_minimap()
            if minimap_action:
                return True

            # PRIORIDADE 2: Sistema de minimapa básico (fallback)
            minimap_enemies = minimap_system.get_priority_targets()
            if minimap_enemies:
                minimap_action = self._engage_minimap_targets(minimap_enemies)
                if minimap_action:
                    return True

            # PRIORIDADE 3: Detecção visual direta (tela principal)
            targets = visual_detector.detect_targets()
            if not targets:
                self.current_target = None
                return False

            # Processar alvos visuais
            combat_targets = self._process_targets(targets)

            # Selecionar melhor alvo
            best_target = self._select_best_target(combat_targets)

            if best_target:
                return self._engage_target(best_target)

            return False

        except Exception as e:
            logger.error(f"Erro no sistema de combate: {e}")
            return False
    
    def _process_targets(self, targets: List[DetectedTarget]) -> List[CombatTarget]:
        """Processar alvos detectados em alvos de combate"""
        combat_targets = []
        current_time = time.time()
        
        for target in targets:
            # Calcular distância do centro (nosso campeão)
            distance = self._calculate_distance(target.position, self.champion_pos)
            
            # Determinar prioridade
            priority = self._calculate_priority(target, distance)
            
            # Criar alvo de combate
            combat_target = CombatTarget(
                target=target,
                distance=distance,
                priority=priority,
                last_seen=current_time
            )
            
            # Verificar histórico
            target_id = f"{target.type}_{target.position[0]}_{target.position[1]}"
            if target_id in self.target_history:
                combat_target.attack_count = self.target_history[target_id].attack_count
            
            combat_targets.append(combat_target)
        
        return combat_targets
    
    def _calculate_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """Calcular distância entre duas posições"""
        return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
    
    def _calculate_priority(self, target: DetectedTarget, distance: float) -> int:
        """Calcular prioridade do alvo"""
        base_priority = self.target_priorities.get(target.type, 10)
        
        # Reduzir prioridade baseada na distância
        distance_penalty = int(distance / 10)
        
        # Aumentar prioridade para alvos próximos
        if distance < self.attack_distance:
            base_priority += 50
        elif distance < self.follow_distance:
            base_priority += 20
        
        # Prioridade extra para campeões inimigos
        if target.type == 'enemy_champion':
            base_priority += 100
            
            # Ainda mais prioridade se muito próximo
            if distance < 100:
                base_priority += 200
        
        return max(0, base_priority - distance_penalty)
    
    def _select_best_target(self, combat_targets: List[CombatTarget]) -> Optional[CombatTarget]:
        """Selecionar o melhor alvo para atacar"""
        if not combat_targets:
            return None
        
        # Filtrar alvos muito distantes
        valid_targets = [t for t in combat_targets if t.distance <= self.follow_distance * 1.5]
        
        if not valid_targets:
            return None
        
        # Priorizar campeões inimigos
        enemy_champions = [t for t in valid_targets if t.target.type == 'enemy_champion']
        if enemy_champions:
            # Pegar o campeão mais próximo
            return min(enemy_champions, key=lambda t: t.distance)
        
        # Se não há campeões, pegar o alvo com maior prioridade
        return max(valid_targets, key=lambda t: t.priority)
    
    def _engage_target(self, combat_target: CombatTarget) -> bool:
        """Engajar um alvo específico"""
        try:
            target = combat_target.target
            distance = combat_target.distance
            current_time = time.time()
            
            x, y = target.position
            
            # Verificar cooldown de ataque
            if current_time - self.last_attack_time < self.attack_cooldown:
                return False
            
            # Estratégia baseada no tipo de alvo e distância
            if target.type == 'enemy_champion':
                return self._attack_enemy_champion(x, y, distance)
            elif target.type == 'enemy_minion' and distance < self.attack_distance:
                return self._attack_minion(x, y)
            
            return False
            
        except Exception as e:
            logger.error(f"Erro ao engajar alvo: {e}")
            return False
    
    def _attack_enemy_champion(self, x: int, y: int, distance: float) -> bool:
        """Atacar campeão inimigo"""
        try:
            current_time = time.time()
            
            if distance <= self.attack_distance:
                # Distância de ataque - atacar diretamente
                logger.info(f"⚔️ ATACANDO CAMPEÃO INIMIGO em ({x}, {y}) - Distância: {distance:.0f}")
                
                # Clique direito para atacar
                input_controller.right_click_at(x, y)
                self.last_attack_time = current_time
                
                # Atualizar alvo atual
                self.current_target = (x, y)
                
                return True
                
            elif distance <= self.follow_distance:
                # Distância de seguimento - mover em direção ao inimigo
                logger.info(f"🏃 SEGUINDO CAMPEÃO INIMIGO em ({x}, {y}) - Distância: {distance:.0f}")
                
                # Mover em direção ao inimigo (um pouco mais próximo)
                approach_x = x + (self.champion_pos[0] - x) * 0.3
                approach_y = y + (self.champion_pos[1] - y) * 0.3
                
                input_controller.right_click_at(int(approach_x), int(approach_y))
                self.last_attack_time = current_time
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Erro ao atacar campeão: {e}")
            return False
    
    def _attack_minion(self, x: int, y: int) -> bool:
        """Atacar minion (farm)"""
        try:
            logger.debug(f"🌾 FARMANDO MINION em ({x}, {y})")
            
            # Clique direito para atacar minion
            input_controller.right_click_at(x, y)
            self.last_attack_time = time.time()
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao atacar minion: {e}")
            return False
    
    def get_combat_status(self) -> dict:
        """Obter status do sistema de combate"""
        return {
            'current_target': self.current_target,
            'last_attack_time': self.last_attack_time,
            'attack_cooldown': self.attack_cooldown,
            'targets_in_history': len(self.target_history)
        }
    
    def set_aggressive_mode(self, aggressive: bool):
        """Configurar modo agressivo"""
        if aggressive:
            self.attack_cooldown = 0.5
            self.follow_distance = 300
            self.attack_distance = 200
            logger.info("🔥 Modo agressivo ativado")
        else:
            self.attack_cooldown = 0.8
            self.follow_distance = 200
            self.attack_distance = 150
            logger.info("🛡️ Modo defensivo ativado")
    
    def _engage_minimap_targets(self, minimap_enemies: List[MinimapEnemy]) -> bool:
        """Engajar alvos detectados no minimapa"""
        try:
            if not minimap_enemies:
                return False

            current_time = time.time()

            # Verificar cooldown
            if current_time - self.last_attack_time < self.attack_cooldown:
                return False

            # Priorizar campeões inimigos
            champions = [e for e in minimap_enemies if e.is_champion]
            if champions:
                target_enemy = champions[0]  # Melhor campeão
            else:
                target_enemy = minimap_enemies[0]  # Melhor alvo geral

            # Converter posição do minimapa para tela
            screen_pos = minimap_system.convert_minimap_to_screen(target_enemy.position)

            # Determinar tipo de ação baseado na distância
            if target_enemy.distance_to_player < 30:  # Muito próximo no minimapa
                logger.info(f"⚔️ ATACANDO INIMIGO DO MINIMAPA ({target_enemy.lane.value}) em {screen_pos}")
                input_controller.right_click_at(screen_pos[0], screen_pos[1])
                self.last_attack_time = current_time
                self.current_target = screen_pos
                return True

            elif target_enemy.distance_to_player < 60:  # Próximo no minimapa
                logger.info(f"🏃 MOVENDO PARA INIMIGO DO MINIMAPA ({target_enemy.lane.value}) em {screen_pos}")
                input_controller.right_click_at(screen_pos[0], screen_pos[1])
                self.last_attack_time = current_time
                return True

            return False

        except Exception as e:
            logger.error(f"Erro ao engajar alvos do minimapa: {e}")
            return False

    def force_attack_position(self, x: int, y: int):
        """Forçar ataque em posição específica"""
        logger.info(f"💥 ATAQUE FORÇADO em ({x}, {y})")
        input_controller.right_click_at(x, y)
        self.last_attack_time = time.time()

    def _process_advanced_minimap(self) -> bool:
        """Processar sistema avançado de minimapa"""
        try:
            # Capturar minimapa
            minimap = advanced_minimap.capture_minimap()
            if minimap is None:
                return False

            # Detectar unidades
            units = advanced_minimap.detect_units(minimap)
            if not units:
                return False

            # Obter ação estratégica
            strategic_action = advanced_minimap.get_strategic_action(units)

            if not strategic_action or not strategic_action.get('target_position'):
                return False

            current_time = time.time()

            # Verificar cooldown
            if current_time - self.last_attack_time < self.attack_cooldown:
                return False

            action_type = strategic_action.get('action_type', 'move')
            target_pos = strategic_action['target_position']
            description = strategic_action.get('description', 'Ação estratégica')

            # Executar ação baseada no tipo
            if action_type == 'attack':
                logger.info(f"⚔️ ATAQUE ESTRATÉGICO: {description}")
                input_controller.right_click_at(target_pos[0], target_pos[1])
                self.last_attack_time = current_time
                self.current_target = target_pos
                return True

            elif action_type == 'retreat':
                logger.warning(f"🏃 RECUO ESTRATÉGICO: {description}")
                input_controller.right_click_at(target_pos[0], target_pos[1])
                self.last_attack_time = current_time
                return True

            elif action_type == 'move':
                # Movimento estratégico (menos frequente)
                if current_time - self.last_attack_time > 2.0:  # Cooldown maior para movimento
                    logger.debug(f"🚶 MOVIMENTO ESTRATÉGICO: {description}")
                    input_controller.right_click_at(target_pos[0], target_pos[1])
                    self.last_attack_time = current_time
                    return True

            return False

        except Exception as e:
            logger.error(f"Erro no minimapa avançado: {e}")
            return False

    def get_minimap_status(self) -> dict:
        """Obter status do sistema de minimapa"""
        try:
            # Status do sistema básico
            minimap_enemies = minimap_system.get_priority_targets()
            champions = [e for e in minimap_enemies if e.is_champion]

            # Status do sistema avançado
            advanced_status = advanced_minimap.get_current_status()

            return {
                'minimap_enemies': len(minimap_enemies),
                'minimap_champions': len(champions),
                'current_lane': minimap_system.current_lane.value if hasattr(minimap_system, 'current_lane') else 'unknown',
                'advanced_state': advanced_status.get('current_state', 'unknown'),
                'ally_minions': advanced_status.get('ally_minions_alive', 0),
                'enemy_units': advanced_status.get('enemy_units', 0),
                'towers_detected': advanced_status.get('towers_detected', 0)
            }
        except Exception as e:
            logger.error(f"Erro ao obter status do minimapa: {e}")
            return {'error': str(e)}

# Instância global
combat_system = CombatSystem()
